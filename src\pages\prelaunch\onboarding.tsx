import {
  ArrowCircleRight2,
  <PERSON><PERSON><PERSON>,
  // Crown,
  Graph,
  Profile2User,
} from "iconsax-react";
import { Button } from "../../components/button/onboardingButton";
import gift from "../../assets/images/gift-box.png";
// import box from '../../assets/images/box.png';
import { useNavigate } from "react-router-dom";
import { Footer } from "./footer";
// import img from '../../assets/images/res.png';
// import { Head } from '../../components/reuseables/head';
import CreateGuestList from "./create-guest-list/create-guest";
import { useEffect, useState } from "react";
// import { CreateGiftRegistry } from './gift-registry/create-gift-registry';
import { useEventStore } from "../../lib/store/event";
import { CreateGiftRegistry } from "./gift-registry/create-gift-registry";
import { Head } from "../../components/reuseables/head";

interface OnboardingProps {
  eventName?: string;
}

export const Onboarding = ({ eventName }: OnboardingProps) => {
  const navigate = useNavigate();
  const { createdEventData, setSelectedEvent } = useEventStore();
  const [isGuestListModalOpen, setIsGuestListModalOpen] = useState(false);
  const [isGiftRegistryModalOpen, setIsGiftRegistryModalOpen] = useState(false);

  useEffect(() => {
    if (isGuestListModalOpen) {
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.width = "100%";
    } else {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
    }

    return () => {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
    };
  }, [isGuestListModalOpen]);
  return (
    <div className="fixed  inset-0 px-4 md:px-0  [&::-webkit-scrollbar]:hidden overflow-y-auto z-50 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="relative">
        <div
          className="absolute inset-0 h-[520px] md:h-[444px] top-0 bg-[url(/src/assets/animations/gift.gif)] opacity-40 z-50"
          style={{ backgroundSize: "cover" }}
        />
        <div className="relative z-50">
          <Head />
          <div className="max-w-[572px]  w-full mx-auto mb-32 pt-10">
            <div className="relative mb-10 rounded-[20px] bg-[#4D55F2] z-10 px-7 py-7">
              <svg
                className="absolute top-0 left-0"
                width="565"
                height="188"
                viewBox="0 0 565 188"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.2"
                  d="M564.312 356.12L553.618 400.023C499.737 384.142 404.928 349.349 344.859 288.824C311.689 296.405 276.752 297.976 241.624 293.112C179.968 284.56 120.995 257.191 73.1254 215.267C27.9781 234.167 -21.3327 236.207 -60.0848 218.511L-44.2082 176.553C-21.2378 187.036 8.58992 187.514 37.9566 179.544C-23.6286 106.703 -13.4568 51.0915 -9.94314 31.7889C1.05899 -28.5974 47.177 -64.3601 99.6377 -53.1848C137.989 -44.9657 179.912 -13.9441 187.075 36.8434C194.559 90.0256 173.546 142.337 129.431 180.416C124.555 184.625 119.494 188.623 114.27 192.312C173.109 237.208 245.345 256.565 312.883 248.186C292.535 214.06 283.663 173.8 294.508 126.543C294.651 125.958 294.805 125.324 294.948 124.739C306.13 78.8354 340.131 43.0567 380.017 35.4156C424.65 26.8211 470.127 53.4004 488.2 98.6199C508.869 150.356 490.267 210.332 442.971 244.492C427.16 255.935 410.231 265.533 392.484 273.216C395.146 275.36 397.865 277.468 400.672 279.603C454.6 320.071 526.352 344.932 564.357 356.133L564.312 356.12ZM357.56 238.244C379.709 231.022 400.863 220.474 420.199 206.463C445.638 188.08 464.813 153.264 450.246 116.869C439.735 90.5566 413.12 75.1311 386.995 80.1358C362.757 84.8079 341.069 108.478 334.442 137.634C326.113 173.973 333.656 207.201 357.515 238.231L357.56 238.244ZM80.0359 161.727C88.4639 156.786 96.458 151.091 103.861 144.699C122.697 128.448 153.208 93.7318 146.197 43.8781C142.106 15.0143 115.104 -3.50554 91.7047 -8.52012C57.0049 -15.9244 36.0375 10.8342 30.5455 40.7948C23.3011 80.4864 39.0161 118.998 80.0241 161.775L80.0359 161.727Z"
                  fill="#9CC1FC"
                />
              </svg>

              <div className="flex items-start justify-between">
                <div>
                  <h1 className="text-[40px] font-bold leading-[0.95] tracking-[-4%] text-white mb-3">
                    Welcome to
                    <br />
                    <span className="text-[#9CC1FC]">Eventpark</span>
                  </h1>
                  <p className="text-base leading-[1.5] tracking-[-3%] text-[#F1F4F7] mb-0 max-w-[343px]">
                    Create your first event and unlock a seamless planning
                    experience.
                    <span className="font-medium italics">
                      {" "}
                      Plan, Book, Celebrate{" "}
                    </span>{" "}
                    .
                  </p>
                </div>

                {/* Small Icon */}
                <div className="w-9 h-9 bg-[#010139] rounded-full flex items-center justify-center mt-1">
                  <svg
                    width="21"
                    height="20"
                    viewBox="0 0 21 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14.828 0.817745C13.9879 1.14897 13.0136 1.87854 12.2276 2.76614C12.0826 2.2509 11.8206 1.73349 11.4352 1.19011C11.2209 0.889186 10.8571 0.731148 10.4912 0.7896C9.88065 0.88702 9.51691 1.52999 9.75291 2.10152C10.1773 3.13417 10.1664 4.28373 9.67929 5.25576C9.40649 5.80131 9.00161 6.2581 8.52095 6.59582C8.53177 6.53088 8.5426 6.47026 8.55126 6.40748C8.75911 4.83577 8.32825 3.16664 7.37343 1.82441C6.43376 0.506 4.63021 0.0167387 3.18174 0.683523C1.24395 1.57546 0.436361 3.5888 1.2223 5.57616C1.55357 6.41614 2.28322 7.39033 3.17092 8.17619C2.65562 8.32123 2.13815 8.58318 1.59254 8.96853C1.29159 9.18286 1.13353 9.54656 1.19199 9.91243C1.28942 10.5229 1.93246 10.8866 2.50406 10.6506C3.53682 10.2263 4.68651 10.2372 5.65865 10.7243C6.20426 10.997 6.6611 11.4019 6.99886 11.8825C6.93391 11.8716 6.87329 11.8608 6.8105 11.8522C5.23861 11.6443 3.5693 12.0751 2.22692 13.032C0.908359 13.9716 0.41904 15.7749 1.0859 17.2232C1.97793 19.1608 3.9915 19.9683 5.97909 19.1825C6.81916 18.8512 7.79347 18.1217 8.57941 17.2341C8.72447 17.7493 8.98645 18.2667 9.37184 18.8101C9.58619 19.111 9.94993 19.2691 10.3158 19.2106C10.9264 19.1132 11.2901 18.4702 11.0541 17.8987C10.6298 16.866 10.6406 15.7165 11.1278 14.7444C11.4006 14.1989 11.8054 13.7421 12.2861 13.4044C12.2753 13.4693 12.2645 13.5299 12.2558 13.5927C12.0479 15.1644 12.4788 16.8336 13.4336 18.1758C14.3733 19.4942 16.1768 19.9835 17.6253 19.3167C19.5631 18.4247 20.3707 16.4114 19.5848 14.424C19.2535 13.5841 18.5238 12.6099 17.6361 11.824C18.1514 11.679 18.6689 11.417 19.2123 11.0317C19.5133 10.8173 19.6714 10.4536 19.6129 10.0878C19.5155 9.47728 18.8724 9.11358 18.3008 9.34956C17.2681 9.77387 16.1184 9.76305 15.1462 9.27595C14.6006 9.00317 14.1438 8.59834 13.806 8.11774C13.871 8.12856 13.9316 8.13938 13.9944 8.14804C15.5663 8.35587 17.2356 7.92506 18.578 6.97034C19.8965 6.03078 20.3858 4.22744 19.719 2.77913C18.827 0.841558 16.8134 0.0340588 14.8258 0.819912L14.828 0.817745ZM3.30732 4.72969C3.1471 4.32486 2.90028 3.35715 3.87891 2.77913C3.93954 2.74233 4.00666 2.70769 4.07811 2.67521C4.58691 2.44141 5.23861 2.71851 5.52874 3.12335C6.16529 4.01744 6.46624 5.1605 6.32984 6.18233C6.29736 6.4248 6.18694 6.78633 6.02889 7.19983C4.74064 6.98117 3.71869 5.78183 3.30299 4.72969H3.30732ZM5.13252 17.0977C4.72764 17.2579 3.75983 17.5047 3.18174 16.5261C3.14494 16.4655 3.11029 16.3984 3.07782 16.327C2.84398 15.8182 3.12112 15.1666 3.526 14.8765C4.4202 14.24 5.56338 13.9391 6.58532 14.0755C6.82782 14.108 7.18939 14.2184 7.60293 14.3764C7.38426 15.6645 6.18478 16.6863 5.13252 17.102V17.0977ZM9.74208 12.8393C9.4736 11.4668 8.70282 10.2155 7.56613 9.33873C8.93882 9.07245 10.1903 8.29959 11.0671 7.16302C11.3356 8.53556 12.1064 9.78686 13.2431 10.6636C11.8704 10.9299 10.619 11.7028 9.74208 12.8393ZM17.4997 15.2727C17.66 15.6775 17.9068 16.6452 16.9281 17.2232C16.8675 17.26 16.8004 17.2947 16.7289 17.3272C16.2201 17.561 15.5684 17.2839 15.2783 16.879C14.6418 15.9849 14.3408 14.8419 14.4772 13.82C14.5097 13.5776 14.6201 13.216 14.7782 12.8025C16.0664 13.0212 17.0884 14.2205 17.5041 15.2727H17.4997ZM17.7292 3.67756C17.9631 4.1863 17.6859 4.83794 17.2811 5.12803C16.3869 5.76451 15.2437 6.06542 14.2217 5.92903C13.9792 5.89656 13.6177 5.78616 13.2041 5.62812C13.4228 4.34001 14.6223 3.31819 15.6745 2.90253C16.0794 2.74233 17.0472 2.49553 17.6253 3.47406C17.6621 3.53467 17.6968 3.60179 17.7292 3.67323V3.67756Z"
                      fill="#B8BBFA"
                    />
                  </svg>
                </div>
              </div>

              {/* Create Event Button */}
              <div className="absolute bottom-7 right-7">
                <button
                  onClick={() => navigate("/event-setup")}
                  className="flex items-center gap-2 bg-white border border-white rounded-full px-4 py-2.5 text-sm font-semibold text-[#343CD8] hover:bg-gray-50 transition-colors shadow-sm"
                >
                  Create an Event
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      opacity="0.4"
                      d="M9.9974 18.3327C14.5998 18.3327 18.3307 14.6017 18.3307 9.99935C18.3307 5.39698 14.5998 1.66602 9.9974 1.66602C5.39502 1.66602 1.66406 5.39698 1.66406 9.99935C1.66406 14.6017 5.39502 18.3327 9.9974 18.3327Z"
                      fill="#343CD8"
                    />
                    <path
                      d="M13.3609 9.5582L10.8609 7.0582C10.6193 6.81654 10.2193 6.81654 9.9776 7.0582C9.73594 7.29987 9.73594 7.69987 9.9776 7.94154L11.4109 9.37487H7.08594C6.74427 9.37487 6.46094 9.6582 6.46094 9.99987C6.46094 10.3415 6.74427 10.6249 7.08594 10.6249H11.4109L9.9776 12.0582C9.73594 12.2999 9.73594 12.6999 9.9776 12.9415C10.1026 13.0665 10.2609 13.1249 10.4193 13.1249C10.5776 13.1249 10.7359 13.0665 10.8609 12.9415L13.3609 10.4415C13.6026 10.1999 13.6026 9.79987 13.3609 9.5582Z"
                      fill="#343CD8"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div className="flex flex-col md:flex-row justify-between">
              <div className="md:max-w-[270px] w-full ">
                <div
                  onClick={() => setIsGuestListModalOpen(true)}
                  className="bg-white cursor-pointer rounded-[20px]  [box-shadow:0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_#0000000F] p-4 md:max-w-[270px] w-full h-[195px] relative overflow-hidden"
                >
                  <div className="relative  flex flex-col justify-between h-full items-start">
                    <svg
                      width="48"
                      height="48"
                      viewBox="0 0 48 48"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle cx="24" cy="24" r="24" fill="#EBF3FE" />
                      <path
                        opacity="0.4"
                        d="M21 14C18.38 14 16.25 16.13 16.25 18.75C16.25 21.32 18.26 23.4 20.88 23.49C20.96 23.48 21.04 23.48 21.1 23.49C21.12 23.49 21.13 23.49 21.15 23.49C21.16 23.49 21.16 23.49 21.17 23.49C23.73 23.4 25.74 21.32 25.75 18.75C25.75 16.13 23.62 14 21 14Z"
                        fill="#365B96"
                      />
                      <path
                        d="M26.0809 26.1509C23.2909 24.2909 18.7409 24.2909 15.9309 26.1509C14.6609 27.0009 13.9609 28.1509 13.9609 29.3809C13.9609 30.6109 14.6609 31.7509 15.9209 32.5909C17.3209 33.5309 19.1609 34.0009 21.0009 34.0009C22.8409 34.0009 24.6809 33.5309 26.0809 32.5909C27.3409 31.7409 28.0409 30.6009 28.0409 29.3609C28.0309 28.1309 27.3409 26.9909 26.0809 26.1509Z"
                        fill="#365B96"
                      />
                      <path
                        opacity="0.4"
                        d="M31.9894 19.3401C32.1494 21.2801 30.7694 22.9801 28.8594 23.2101C28.8494 23.2101 28.8494 23.2101 28.8394 23.2101H28.8094C28.7494 23.2101 28.6894 23.2101 28.6394 23.2301C27.6694 23.2801 26.7794 22.9701 26.1094 22.4001C27.1394 21.4801 27.7294 20.1001 27.6094 18.6001C27.5394 17.7901 27.2594 17.0501 26.8394 16.4201C27.2194 16.2301 27.6594 16.1101 28.1094 16.0701C30.0694 15.9001 31.8194 17.3601 31.9894 19.3401Z"
                        fill="#365B96"
                      />
                      <path
                        d="M33.9922 28.5904C33.9122 29.5604 33.2922 30.4004 32.2522 30.9704C31.2522 31.5204 29.9922 31.7804 28.7422 31.7504C29.4622 31.1004 29.8822 30.2904 29.9622 29.4304C30.0622 28.1904 29.4722 27.0004 28.2922 26.0504C27.6222 25.5204 26.8422 25.1004 25.9922 24.7904C28.2022 24.1504 30.9822 24.5804 32.6922 25.9604C33.6122 26.7004 34.0822 27.6304 33.9922 28.5904Z"
                        fill="#365B96"
                      />
                    </svg>

                    <div>
                      <p className=" text-xs uppercase text-primary-750 tracking-[0.12em] mb-2">
                        Guest Management
                      </p>
                      <p className="text-xs md:text-xl font-medium">
                        Add & manage
                        <br /> guest for your event{" "}
                      </p>
                    </div>
                  </div>
                  <div className="absolute bottom-[-20px] right-[-10px] pointer-events-none">
                    <div className="bg-[#EBF3FE] rounded-lg p-3 w-[120px] shadow-sm">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center">
                            <span className="text-[#00008C] text-xs font-semibold">
                              OR
                            </span>
                          </div>
                          <div>
                            <p className="text-[#00008C] text-xs font-semibold">
                              Olivia Rhye
                            </p>
                            <p className="text-[#535862] text-[10px]">
                              <EMAIL>
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-purple-200 rounded-full flex items-center justify-center">
                            <span className="text-[#00008C] text-xs font-semibold">
                              JM
                            </span>
                          </div>
                          <div>
                            <p className="text-[#00008C] text-xs font-semibold">
                              Jacon Meyer
                            </p>
                            <p className="text-[#535862] text-[10px]">
                              <EMAIL>
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-green-200 rounded-full flex items-center justify-center">
                            <span className="text-[#00008C] text-xs font-semibold">
                              RD
                            </span>
                          </div>
                          <div>
                            <p className="text-[#00008C] text-xs font-semibold">
                              Ray Donovan
                            </p>
                            <p className="text-[#535862] text-[10px]">
                              donovan@untit...
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-white rounded-[20px] mt-5 p-4 md:max-w-[270px] w-full h-[195px] relative overflow-hidden opacity-60">
                  <div className="relative  flex flex-col justify-between h-full items-start">
                    <div className="flex justify-between items-center w-full">
                      <div className="bg-primary-700 p-3 rounded-full">
                        <Graph
                          className="w-7 h-7"
                          color="#365B96"
                          variant="Bulk"
                        />
                      </div>
                      <div className="bg-white px-3 py-1 rounded-full border border-gray-300">
                        <span className="text-xs font-semibold text-primary-750 tracking-wider">
                          COMING SOON..
                        </span>
                      </div>
                    </div>

                    <div>
                      <p className=" text-xs uppercase text-primary-750 tracking-[0.12em] mb-2">
                        BUDGET PLANNER{" "}
                      </p>
                      <p className="text-xs md:text-xl font-medium">
                        Plan a Budget for <br /> your event
                      </p>
                    </div>
                  </div>
                  <div className="absolute bottom-[-25px] right-[-10px] pointer-events-none ">
                    <Graph
                      color="#E5E3FF"
                      className="w-[140px] max-h-[140px]"
                      variant="Bulk"
                    />
                  </div>
                  {/* Coming Soon Watermark */}
                  <div className="absolute inset-0 flex items-start pt-7  justify-end bg-black/5 rounded-[20px]"></div>
                </div>
              </div>
              <div
                // onClick={() => navigate('/create-gift-registry')}
                onClick={() => setIsGiftRegistryModalOpen(true)}
                className="md:max-w-[270px] cursor-pointer  mt-6 md:mt-0 flex flex-col justify-between  w-full rounded-[20px] bg-[linear-gradient(180deg,_#1A22BF_13.41%,_#000059_100%)]"
              >
                <div className="pt-8 pl-4">
                  <h3 className="text-xs text-primary-200 mb-1 tracking-[0.12em]">
                    GIFT REGISTRY
                  </h3>
                  <p className="text-2xl font-medium text-primary-900">
                    Curate gifts for <br /> your event and share with friends 🎉
                  </p>
                </div>
                <img src={gift} alt="gift" />
              </div>
            </div>
            {/* <div className="bg-white mt-5 px-6 pt-6  rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F] flex justify-between md:flex-row flex-col">
              <div>
                <h3 className="mb-2 text-cus-orange-100 text-xs tracking-[0.12em]">
                  EVENT WEBSITES
                </h3>
                <p className="text-2xl font-semibold text-cus-orange-200 mb-5">
                  Create a Website <br /> particular to your event
                </p>
                <div className="border border-pink-400 w-fit p-0.5 rounded-full mb-9">
                  <Button
                    variant="primary"
                    size="sm"
                    className="bg-[linear-gradient(184.41deg,_#FFFCFB_3.55%,_#FDF2ED_96.42%)] text-grey-500"
                    iconLeft={
                      <Crown size="12" color="#967F75" variant="Bulk" />
                    }>
                    Coming Soon
                  </Button>
                </div>
              </div>
              <img src={box} alt="box" />
            </div> */}
          </div>
          <Footer />
        </div>
      </div>
      {isGuestListModalOpen && (
        <div className="fixed inset-0 z-50 bg-white overflow-y-auto">
          <div className="min-h-screen">
            <CreateGuestList onClose={() => navigate("/")} />
          </div>
        </div>
      )}
      {isGiftRegistryModalOpen && (
        <CreateGiftRegistry onClose={() => navigate("/")} />
      )}
    </div>
  );
};
